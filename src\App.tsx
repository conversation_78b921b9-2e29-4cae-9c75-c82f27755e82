import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import NotFound from './components/NotFound';
import ChatWindow from './components/ChatWindow';
import { initGA, trackPageView } from './utils/analytics';

// Initialize analytics
initGA();

// Component to track page views
function PageViewTracker() {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname);
  }, [location]);

  return null;
}

function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-950 via-neutral-900 to-black relative overflow-hidden flex flex-col">
      {/* Enhanced grid overlay with multiple layers */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.03),transparent_70%)]" />

      {/* Floating background elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl float opacity-30"></div>
      <div className="absolute top-40 right-20 w-24 h-24 bg-gray-300/5 rounded-full blur-lg float-delayed opacity-40"></div>
      <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white/3 rounded-full blur-2xl float-gentle opacity-20"></div>

      {/* Header with Logo */}
      <div className="relative z-10 flex justify-center py-8 px-4 fade-in">
        <div className="relative">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight select-none" style={{ fontFamily: "'Inter', 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", fontWeight: 700, color: 'transparent', WebkitTextStroke: '1px white' }}>
            */now<span className="text-white" style={{ color: 'white', WebkitTextStroke: 'none' }}>.</span>
          </h1>
        </div>
      </div>

      {/* Chat Window */}
      <div className="relative z-10 flex-1 px-4 pb-4 fade-in-delayed">
        <div className="max-w-4xl mx-auto h-full">
          <ChatWindow />
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <PageViewTracker />
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;















