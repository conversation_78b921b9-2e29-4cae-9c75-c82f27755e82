import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import NotFound from './components/NotFound';
import { initGA, trackPageView, track404Error } from './utils/analytics';

// Initialize analytics
initGA();

// Component to track page views
function PageViewTracker() {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname);
  }, [location]);

  return null;
}

function Home() {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Subtle grid overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center">
        {/* Logo/Brand */}
        <div className="mb-12 relative">
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold text-white leading-tight tracking-tight select-none outline-text">
            */now<span className="text-white">.</span>
          </h1>
        </div>

        {/* Coming Soon Text */}
        <div className="mb-16">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-full px-8 py-4 shadow-lg">
            <p className="text-lg md:text-xl text-white/90 font-medium tracking-wide">
              Coming Soon
            </p>
          </div>
        </div>

        {/* Minimal decorative elements */}
        <div className="w-16 h-px bg-white/20 mb-8" />
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <PageViewTracker />
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;















