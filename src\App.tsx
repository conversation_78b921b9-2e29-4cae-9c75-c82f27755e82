import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import NotFound from './components/NotFound';
import ChatWindow from './components/ChatWindow';
import { initGA, trackPageView } from './utils/analytics';

// Initialize analytics
initGA();

// Component to track page views
function PageViewTracker() {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname);
  }, [location]);

  return null;
}

function Home() {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden flex flex-col">
      {/* Subtle grid overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      {/* Header with Logo */}
      <div className="relative z-10 flex justify-center py-8 px-4">
        <div className="relative">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight tracking-tight select-none outline-text">
            */now<span className="text-white">.</span>
          </h1>
        </div>
      </div>

      {/* Chat Window */}
      <div className="relative z-10 flex-1 px-4 pb-4">
        <div className="max-w-4xl mx-auto h-full">
          <ChatWindow />
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <PageViewTracker />
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;















