import { ChatMessage as ChatMessageType } from '../hooks/useChat';

interface ChatMessageProps {
  message: ChatMessageType;
}

export default function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.role === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-3 transition-opacity duration-150 opacity-0 animate-[fadeIn_0.2s_ease-out_forwards]`}>
      <div
        className={`
          max-w-[85%] px-4 py-3 rounded-2xl backdrop-blur-md border relative overflow-hidden
          transition-all duration-150
          ${isUser
            ? 'bg-gradient-to-br from-white/15 via-white/10 to-gray-300/5 border-white/20 text-white'
            : 'bg-gradient-to-br from-white/8 via-gray-300/5 to-white/3 border-white/10 text-white/95'
          }
        `}
      >
        {/* Subtle gradient overlay */}
        <div className={`absolute inset-0 rounded-2xl ${isUser ? 'bg-gradient-to-br from-white/5 to-transparent' : 'bg-gradient-to-br from-gray-300/3 to-transparent'} pointer-events-none`}></div>

        <div className="relative z-10">
          <p className="text-sm leading-relaxed whitespace-pre-wrap font-medium">
            {message.content}
          </p>
          <div className={`mt-2 text-xs ${isUser ? 'text-white/50' : 'text-gray-300/60'} font-medium`}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    </div>
  );
}
