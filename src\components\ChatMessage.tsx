import { ChatMessage as ChatMessageType } from '../hooks/useChat';

interface ChatMessageProps {
  message: ChatMessageType;
}

export default function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.role === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4 animate-in slide-in-from-bottom-2 duration-300`}>
      <div
        className={`
          max-w-[80%] px-4 py-3 rounded-2xl backdrop-blur-md border shadow-lg relative overflow-hidden
          transition-all duration-200
          ${isUser
            ? 'bg-gradient-to-br from-white/15 via-white/10 to-gray-300/5 border-white/20 text-white shadow-white/5'
            : 'bg-gradient-to-br from-white/8 via-gray-300/5 to-white/3 border-white/10 text-white/90 shadow-black/20'
          }
        `}
      >
        {/* Subtle gradient overlay */}
        <div className={`absolute inset-0 rounded-2xl ${isUser ? 'bg-gradient-to-br from-white/5 to-transparent' : 'bg-gradient-to-br from-gray-300/3 to-transparent'} pointer-events-none`}></div>

        <div className="relative z-10">
          <p className="text-sm leading-relaxed whitespace-pre-wrap font-medium">
            {message.content}
          </p>
          <div className={`mt-2 text-xs ${isUser ? 'text-white/50' : 'text-gray-300/60'} font-medium`}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    </div>
  );
}
