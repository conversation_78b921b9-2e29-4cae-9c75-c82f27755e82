import { ChatMessage as ChatMessageType } from '../hooks/useChat';

interface ChatMessageProps {
  message: ChatMessageType;
}

export default function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.role === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-3 transition-opacity duration-150 opacity-0 animate-[fadeIn_0.2s_ease-out_forwards]`}>
      <div
        className={`
          max-w-[80%] px-5 py-3 rounded-3xl backdrop-blur-xl border-0 relative overflow-hidden
          transition-all duration-150 shadow-lg shadow-black/10
          ${isUser
            ? 'bg-white/12 text-white'
            : 'bg-white/8 text-white/95'
          }
        `}
      >
        <div className="relative z-10">
          <p className="text-sm leading-relaxed whitespace-pre-wrap font-normal">
            {message.content}
          </p>
          <div className={`mt-2 text-xs ${isUser ? 'text-white/40' : 'text-gray-300/50'} font-normal`}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    </div>
  );
}
