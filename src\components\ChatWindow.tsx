import { useEffect, useRef } from 'react';
import { useChat } from '../hooks/useChat';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

export default function ChatWindow() {
  const { messages, isLoading, sendMessage } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-black/30 via-zinc-950/20 to-neutral-900/30 backdrop-blur-md border border-white/10 rounded-t-3xl overflow-hidden shadow-2xl shadow-black/50 pulse-glow">
      {/* Enhanced gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-gray-300/5 pointer-events-none rounded-t-3xl"></div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4 chat-messages relative z-10">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center fade-in-delayed-2">
              <div className="relative mb-8">
                <div className="w-16 h-16 mx-auto bg-gradient-to-br from-white/10 to-gray-300/5 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/10 shadow-lg float-gentle">
                  <div className="w-8 h-8 bg-gradient-to-br from-white/20 to-transparent rounded-full"></div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent blur-xl"></div>
              </div>
              <div className="bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent text-xl font-semibold mb-3">
                Start a conversation
              </div>
              <div className="text-gray-300 text-sm font-medium">
                Ask anything to get started
              </div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            {isLoading && (
              <div className="flex justify-start mb-4 fade-in">
                <div className="bg-gradient-to-br from-white/10 via-gray-300/5 to-white/5 border border-white/10 rounded-2xl px-4 py-3 backdrop-blur-md shadow-lg">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gradient-to-r from-white/60 to-gray-300/40 rounded-full animate-bounce shadow-sm" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 bg-gradient-to-r from-white/60 to-gray-300/40 rounded-full animate-bounce shadow-sm" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 bg-gradient-to-r from-white/60 to-gray-300/40 rounded-full animate-bounce shadow-sm" style={{ animationDelay: '300ms' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input Area */}
      <ChatInput onSendMessage={sendMessage} isLoading={isLoading} />
    </div>
  );
}
