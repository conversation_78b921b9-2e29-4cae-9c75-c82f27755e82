import { useEffect, useRef, useState } from 'react';
import { useChat } from '../hooks/useChat';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

export default function ChatWindow() {
  const { messages, isLoading, sendMessage } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [hasMessages, setHasMessages] = useState(false);

  const scrollToBottom = () => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'auto' }); // Changed to 'auto' for faster scrolling
    }
  };

  useEffect(() => {
    scrollToBottom();
    setHasMessages(messages.length > 0);
  }, [messages]);

  return (
    <div className="h-screen flex flex-col relative overflow-hidden">


      {/* Main Content Area */}
      <div className="flex flex-col h-full transition-all duration-150">
        {/* Center Logo when no messages */}
        {!hasMessages && (
          <div className="flex justify-center items-center py-16 px-4">
            <div className="text-center">
              <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold leading-tight tracking-tight select-none mb-8" style={{ fontFamily: "'Inter', 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", fontWeight: 700, color: 'transparent', WebkitTextStroke: '2px white' }}>
                */now<span className="text-white" style={{ color: 'white', WebkitTextStroke: 'none' }}>.</span>
              </h1>
            </div>
          </div>
        )}

        {/* Chat Container */}
        <div className={`flex-1 ${hasMessages ? 'bg-gradient-to-br from-black/10 via-zinc-950/5 to-neutral-900/10 backdrop-blur-xl overflow-hidden' : ''}`}>
          {hasMessages && (
            <div className="absolute inset-0 bg-gradient-to-br from-white/2 via-transparent to-gray-300/2 pointer-events-none"></div>
          )}

          {messages.length === 0 ? (
            <>
              {/* Messages Area - No Messages */}
              <div className="flex-1 overflow-y-auto chat-messages relative z-10 p-6">
                <div className="flex items-center justify-center h-full min-h-[60vh]">
                  <div className="text-center max-w-md mx-auto">
                    <div className="bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent text-2xl font-normal mb-4">
                      Start a conversation
                    </div>
                    <div className="text-gray-400 text-base font-normal">
                      Ask anything to get started
                    </div>
                  </div>
                </div>
              </div>
              {/* Input Area */}
              <ChatInput onSendMessage={sendMessage} isLoading={isLoading} />
            </>
          ) : (
            <>
              {/* Two Pane Layout */}
              <div className="flex h-full relative z-10">
                {/* Left Pane - Conversation History */}
                <div className="w-1/3 border-r border-white/10 flex flex-col">
                  {/* Logo in top-left */}
                  <div className="p-4 border-b border-white/10">
                    <h1 className="text-2xl font-bold leading-tight tracking-tight select-none" style={{ fontFamily: "'Inter', 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", fontWeight: 700, color: 'transparent', WebkitTextStroke: '1px white' }}>
                      */now<span className="text-white" style={{ color: 'white', WebkitTextStroke: 'none' }}>.</span>
                    </h1>
                  </div>

                  {/* Conversation List */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-3">
                    <div className="text-white/70 text-sm font-medium mb-3">Recent Conversations</div>
                    {messages.filter(m => m.role === 'user').map((message, index) => (
                      <div key={index} className="bg-white/5 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/5 hover:bg-white/8 transition-colors cursor-pointer">
                        <p className="text-white/80 text-sm truncate">{message.content}</p>
                        <p className="text-white/40 text-xs mt-1">Just now</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right Pane - Results Summary */}
                <div className="flex-1 flex flex-col">
                  <div className="p-4">
                    <h2 className="text-white/90 text-lg font-semibold">Results Summary</h2>
                  </div>

                  <div className="flex-1 overflow-y-auto px-6 py-4 space-y-2">
                    {messages.map((message) => (
                      <ChatMessage key={message.id} message={message} />
                    ))}
                    {isLoading && (
                      <div className="flex justify-start mb-3">
                        <div className="bg-white/8 rounded-3xl px-5 py-3 backdrop-blur-xl shadow-lg shadow-black/10">
                          <div className="flex items-center space-x-2">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                              <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '100ms' }}></div>
                              <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </div>
              </div>

              {/* Input Area - Full Width */}
              <div>
                <ChatInput onSendMessage={sendMessage} isLoading={isLoading} />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
