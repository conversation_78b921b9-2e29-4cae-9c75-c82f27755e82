import { useEffect, useRef, useState } from 'react';
import { useChat } from '../hooks/useChat';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

export default function ChatWindow() {
  const { messages, isLoading, sendMessage } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [hasMessages, setHasMessages] = useState(false);

  const scrollToBottom = () => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'auto' }); // Changed to 'auto' for faster scrolling
    }
  };

  useEffect(() => {
    scrollToBottom();
    setHasMessages(messages.length > 0);
  }, [messages]);

  return (
    <div className="min-h-screen flex flex-col relative">
      {/* Logo - positioned top-left when there are messages */}
      {hasMessages && (
        <div className="absolute top-4 left-4 z-20 transition-all duration-150">
          <h1 className="text-lg font-bold leading-tight tracking-tight select-none" style={{ fontFamily: "'Inter', 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", fontWeight: 700, color: 'transparent', WebkitTextStroke: '1px white' }}>
            */now<span className="text-white" style={{ color: 'white', WebkitTextStroke: 'none' }}>.</span>
          </h1>
        </div>
      )}

      {/* Main Content Area */}
      <div className={`flex flex-col h-full transition-all duration-150 ${hasMessages ? 'pt-16' : ''}`}>
        {/* Center Logo when no messages */}
        {!hasMessages && (
          <div className="flex justify-center py-8 px-4">
            <div className="relative">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight select-none" style={{ fontFamily: "'Inter', 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", fontWeight: 700, color: 'transparent', WebkitTextStroke: '1px white' }}>
                */now<span className="text-white" style={{ color: 'white', WebkitTextStroke: 'none' }}>.</span>
              </h1>
            </div>
          </div>
        )}

        {/* Chat Container */}
        <div className={`flex-1 ${hasMessages ? 'bg-gradient-to-br from-black/20 via-zinc-950/10 to-neutral-900/20 backdrop-blur-xl border border-white/8 rounded-3xl overflow-hidden shadow-2xl shadow-black/30 mx-4 mb-4' : ''}`}>
          {hasMessages && (
            <div className="absolute inset-0 bg-gradient-to-br from-white/3 via-transparent to-gray-300/3 pointer-events-none rounded-3xl"></div>
          )}

          {/* Messages Area */}
          <div className={`flex-1 overflow-y-auto chat-messages relative z-10 ${hasMessages ? 'p-8 space-y-3 max-w-5xl mx-auto' : 'p-6'}`}>
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full min-h-[60vh]">
                <div className="text-center max-w-md mx-auto">
                  <div className="bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent text-2xl font-normal mb-4">
                    Start a conversation
                  </div>
                  <div className="text-gray-400 text-base font-normal">
                    Ask anything to get started
                  </div>
                </div>
              </div>
            ) : (
              <>
                {messages.map((message) => (
                  <ChatMessage key={message.id} message={message} />
                ))}
                {isLoading && (
                  <div className="flex justify-start mb-4">
                    <div className="bg-white/8 rounded-3xl px-6 py-4 backdrop-blur-xl shadow-lg shadow-black/10">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                          <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '100ms' }}></div>
                          <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </>
            )}
          </div>

          {/* Input Area */}
          <ChatInput onSendMessage={sendMessage} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
}
