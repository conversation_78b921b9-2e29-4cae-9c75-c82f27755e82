@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure smooth font rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #000;
}

/* Selection styling */
::selection {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Focus styles */
*:focus {
  outline: 2px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Outlined text utility */
.outline-text {
  color: transparent;
  -webkit-text-stroke: 2px white;
  text-stroke: 2px white;
}



