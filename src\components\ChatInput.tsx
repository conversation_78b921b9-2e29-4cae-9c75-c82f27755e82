import React, { useState, KeyboardEvent, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {
  const [input, setInput] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = () => {
    if (input.trim() && !isLoading) {
      onSendMessage(input.trim());
      setInput('');
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = '48px';
      }
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = '48px';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [input]);

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="border-t border-white/10 bg-gradient-to-r from-black/60 via-zinc-950/40 to-black/60 backdrop-blur-md p-4 relative">
      {/* Enhanced gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/3 via-transparent to-gray-300/3 pointer-events-none"></div>

      <div className="flex items-end gap-3 relative z-10">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask anything..."
            className="
              w-full bg-gradient-to-br from-white/8 via-gray-300/5 to-white/3 border border-white/10 rounded-2xl px-4 py-3 pr-12
              text-white placeholder-gray-300/60 resize-none font-medium
              focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/20 focus:shadow-lg focus:shadow-white/10
              backdrop-blur-md transition-all duration-300 hover:bg-white/10
            "
            rows={1}
            style={{
              minHeight: '48px',
              maxHeight: '120px',
              resize: 'none',
            }}
            disabled={isLoading}
          />
        </div>
        <button
          onClick={handleSubmit}
          disabled={!input.trim() || isLoading}
          className="
            p-3 bg-gradient-to-br from-white/15 via-white/10 to-gray-300/5
            hover:from-white/25 hover:via-white/20 hover:to-gray-300/10
            disabled:from-white/5 disabled:via-white/3 disabled:to-transparent
            border border-white/20 hover:border-white/30 disabled:border-white/10
            rounded-xl shadow-lg hover:shadow-xl hover:shadow-white/10
            text-white disabled:text-white/40
            transition-all duration-300 hover:scale-105 active:scale-95
            backdrop-blur-md
            disabled:cursor-not-allowed disabled:hover:scale-100
          "
        >
          <Send size={18} />
        </button>
      </div>
    </div>
  );
}
