import { useState, KeyboardEvent, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {
  const [input, setInput] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = () => {
    if (input.trim() && !isLoading) {
      onSendMessage(input.trim());
      setInput('');
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = '48px';
      }
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = '48px';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [input]);

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="border-t border-white/10 bg-gradient-to-r from-black/40 via-zinc-950/30 to-black/40 backdrop-blur-lg p-4 relative">
      {/* Enhanced gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/2 via-white/1 to-white/2 pointer-events-none"></div>

      <div className="flex items-end gap-3 relative z-10">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask anything..."
            className={`
              w-full border border-white/15 rounded-2xl px-4 py-3 pr-12
              text-white/95 placeholder-white/60 resize-none font-medium
              focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/25
              backdrop-blur-lg transition-all duration-200 hover:border-white/20
              ${input.trim()
                ? 'bg-gradient-to-br from-white/15 via-white/10 to-white/8 focus:bg-white/18 hover:bg-white/16'
                : 'bg-gradient-to-br from-white/8 via-white/5 to-white/3 focus:bg-white/12 hover:bg-white/10'
              }
            `}
            rows={1}
            style={{
              minHeight: '48px',
              maxHeight: '120px',
              resize: 'none',
            }}
            disabled={isLoading}
          />
        </div>
        <button
          onClick={handleSubmit}
          disabled={!input.trim() || isLoading}
          className="
            p-3 bg-gradient-to-br from-white/10 via-white/6 to-white/4
            hover:from-white/15 hover:via-white/10 hover:to-white/6
            disabled:from-white/3 disabled:via-white/2 disabled:to-white/1
            border border-white/20 hover:border-white/25 disabled:border-white/10
            rounded-xl
            text-white disabled:text-white/40
            transition-all duration-200
            backdrop-blur-lg
            disabled:cursor-not-allowed
          "
        >
          <Send size={18} />
        </button>
      </div>
    </div>
  );
}
