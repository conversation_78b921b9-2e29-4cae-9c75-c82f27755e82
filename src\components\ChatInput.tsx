import { useState, KeyboardEvent, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {
  const [input, setInput] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = () => {
    if (input.trim() && !isLoading) {
      onSendMessage(input.trim());
      setInput('');
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = '48px';
      }
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = '48px';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [input]);

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="bg-black/10 backdrop-blur-xl p-6 relative">
      <div className="flex items-end gap-4 relative z-10 max-w-4xl mx-auto">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask anything..."
            className={`
              w-full border-0 rounded-3xl px-6 py-4 pr-14
              text-black/90 placeholder-black/60 resize-none font-normal text-base
              focus:outline-none focus:ring-1 focus:ring-black/20
              backdrop-blur-xl transition-all duration-150
              shadow-lg shadow-black/20
              ${input.trim()
                ? 'bg-white/90 focus:bg-white/95'
                : 'bg-white/80 focus:bg-white/90 hover:bg-white/85'
              }
            `}
            rows={1}
            style={{
              minHeight: '56px',
              maxHeight: '140px',
              resize: 'none',
            }}
            disabled={isLoading}
          />
        </div>
        <button
          onClick={handleSubmit}
          disabled={!input.trim() || isLoading}
          className="
            p-4 bg-white/10 hover:bg-white/15 disabled:bg-white/5
            border-0 rounded-2xl
            text-white/90 disabled:text-white/30
            transition-all duration-150
            backdrop-blur-xl shadow-lg shadow-black/20
            disabled:cursor-not-allowed
            hover:scale-105 active:scale-95
          "
        >
          <Send size={20} />
        </button>
      </div>
    </div>
  );
}
